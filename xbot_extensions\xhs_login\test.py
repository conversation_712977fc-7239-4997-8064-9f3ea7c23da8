# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import xbot
import xbot_visual
from xbot import print, sleep
from .import package
from .package import variables as glv
from ._core import xhs_cookie_login, validate_login_status


def test_xhs_cookie_login():
    """
    测试小红书Cookie登录功能
    """
    print("开始测试小红书Cookie登录功能...")
    
    # 示例Cookie文件路径（请根据实际情况修改）
    cookie_file_path = r"C:\Users\<USER>\Downloads\xhs_cookie.json"
    
    try:
        # 获取当前激活的Chrome浏览器
        web_page = xbot_visual.web.get(web_type="chrome", mode="activated", 
                                     value="", use_wildcard=False, 
                                     silent_running=False, wait_load_completed=True, 
                                     load_timeout="20", stop_load_if_load_timeout="handleExcept", 
                                     open_page=False, url=None)
        
        print("成功获取浏览器对象")
        
        # 执行Cookie登录
        login_result = xhs_cookie_login(web_page, cookie_file_path, add_local_storage=True)
        
        if login_result:
            print("Cookie设置成功")
            
            # 验证登录状态
            validation_result = validate_login_status(web_page)
            
            if validation_result:
                print("✅ 小红书登录测试成功！")
            else:
                print("❌ 小红书登录验证失败")
        else:
            print("❌ Cookie设置失败")
            
    except Exception as e:
        print(f"测试过程中出现异常: {e}")


def test_validate_login_status():
    """
    测试登录状态验证功能
    """
    print("开始测试登录状态验证功能...")
    
    try:
        # 获取当前激活的Chrome浏览器
        web_page = xbot_visual.web.get(web_type="chrome", mode="activated", 
                                     value="", use_wildcard=False, 
                                     silent_running=False, wait_load_completed=True, 
                                     load_timeout="20", stop_load_if_load_timeout="handleExcept", 
                                     open_page=False, url=None)
        
        # 验证当前登录状态
        validation_result = validate_login_status(web_page)
        
        if validation_result:
            print("✅ 当前已登录小红书")
        else:
            print("❌ 当前未登录小红书")
            
    except Exception as e:
        print(f"验证登录状态时出现异常: {e}")


def main(args):
    """
    主测试函数
    """
    print("=== 小红书Cookie登录模块测试 ===")
    
    # 测试登录状态验证
    test_validate_login_status()
    
    print("\n" + "="*50 + "\n")
    
    # 测试Cookie登录（需要有效的Cookie文件）
    test_xhs_cookie_login()
    
    print("\n=== 测试完成 ===")
