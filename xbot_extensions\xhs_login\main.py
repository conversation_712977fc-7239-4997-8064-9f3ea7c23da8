import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    """
    小红书Cookie登录主流程示例
    
    使用说明：
    1. 确保已有有效的小红书Cookie文件
    2. 修改Cookie文件路径为实际路径
    3. 确保Chrome浏览器已打开
    """
    try:
        # 获取已打开的Chrome浏览器
        for _xbot_retry_time in range(4):
            try:
                web_page = xbot_visual.web.get(web_type="chrome", mode="activated", 
                                             value="", use_wildcard=False, 
                                             silent_running=False, wait_load_completed=True, 
                                             load_timeout="20", stop_load_if_load_timeout="handleExcept", 
                                             open_page=False, url=None, 
                                             _block=("main", 1, "获取已打开的网页对象"))
                break
            except Exception as e:
                if _xbot_retry_time == 3:
                    raise e
                else:
                    xbot_visual.programing.log(type='info', text=e, 
                                             _block=("main", 1, "获取已打开的网页对象"))
            time.sleep(3)
        
        # 执行小红书Cookie登录流程
        # 注意：请将Cookie文件路径修改为实际路径
        登录结果 = xbot_visual.process.run(process="process1", package=__name__, inputs={
            "网页对象": web_page,
            "Cookie文件路径": r"C:\Users\<USER>\Downloads\xhs_cookie.json",  # 请修改为实际路径
            "验证登录状态": True,
        }, outputs=[
        ], _block=("main", 2, "调用小红书Cookie登录流程"))
        
        if 登录结果:
            xbot_visual.programing.log(type='info', text="小红书登录成功！", 
                                     _block=("main", 3, "记录成功日志"))
        else:
            xbot_visual.programing.log(type='error', text="小红书登录失败，请检查Cookie文件是否有效", 
                                     _block=("main", 4, "记录失败日志"))
            
    except Exception as e:
        xbot_visual.programing.log(type='error', text=f"执行过程中出现异常: {str(e)}", 
                                 _block=("main", 5, "记录异常日志"))
    finally:
        pass
