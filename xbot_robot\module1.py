# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import xbot
import xbot_visual
from xbot_visual.web import get_cookies, set_cookie, remove_cookie
from xbot import print, sleep
from .import package
from .package import variables as glv
import json
import os
import time

print = [lambda *_, **__: None, print][__package__ == "xbot_robot" or hasattr(xbot, "__log")]


def xhs_cookie_login(web_page, cookie_file_path, add_local_storage=True, validate_login=True):
    """
    小红书Cookie登录功能

    Args:
        web_page: xbot网页对象
        cookie_file_path: Cookie文件路径
        add_local_storage: 是否添加本地存储，默认True
        validate_login: 是否验证登录状态，默认True

    Returns:
        bool: 登录是否成功
    """
    print(f"开始小红书Cookie登录，Cookie文件: {cookie_file_path}")

    # 检查Cookie文件是否存在
    if not os.path.exists(cookie_file_path):
        print(f"错误: Cookie文件不存在: {cookie_file_path}")
        return False

    # 读取Cookie文件
    try:
        with open(cookie_file_path, 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)

        # 检查Cookie文件格式
        if not isinstance(cookie_data, dict):
            error_msg = f"错误: Cookie文件格式不正确，应为JSON对象，当前类型: {type(cookie_data)}"
            print(error_msg)
            return False

        cookies = cookie_data.get('cookies', [])
        local_storage = cookie_data.get('localStorage', {})

        if not isinstance(cookies, list):
            print("警告: cookies字段不是列表格式，将使用空列表")
            cookies = []

        if not isinstance(local_storage, dict):
            print("警告: localStorage字段不是字典格式，将使用空字典")
            local_storage = {}

        print(f"成功读取 {len(cookies)} 个cookie和 {len(local_storage)} 个localStorage项")
    except FileNotFoundError:
        print(f"错误: Cookie文件不存在: {cookie_file_path}")
        return False
    except json.JSONDecodeError as e:
        print(f"错误: Cookie文件JSON格式错误: {e}")
        return False
    except UnicodeDecodeError as e:
        print(f"错误: Cookie文件编码错误: {e}")
        return False
    except Exception as e:
        print(f"读取Cookie文件时出现未知错误: {e}")
        return False

    try:
        # 访问小红书网站
        print("访问小红书网站...")
        web_page.navigate("https://www.xiaohongshu.com", load_timeout=20)
        sleep(2)

        # 清除现有Cookie
        print("清除现有Cookie...")
        clear_cookies(web_page)

        # 设置Cookie
        print("开始设置Cookie...")
        for cookie in cookies:
            if 'name' in cookie and 'value' in cookie and cookie['name'] != "":
                cookie_dict = {
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie.get('domain', '.xiaohongshu.com'),
                    'path': cookie.get('path', '/'),
                }

                # 可选字段
                if 'expires' in cookie and cookie['expires']:
                    cookie_dict['expires'] = cookie['expires']
                if 'secure' in cookie:
                    cookie_dict['secure'] = cookie['secure']
                if 'httpOnly' in cookie:
                    cookie_dict['httpOnly'] = cookie['httpOnly']

                try:
                    set_cookie(url_type="auto", browser=web_page, web_type=None, url=None, **cookie_dict)
                    print(f"设置Cookie: {cookie['name']}")
                except Exception as e:
                    print(f"设置Cookie {cookie['name']} 时出错: {e}")

        # 设置localStorage
        if add_local_storage and local_storage:
            print("开始设置localStorage...")
            for key, value in local_storage.items():
                try:
                    if isinstance(value, str):
                        value = f'`{value}`'
                    web_page.execute_javascript('''
                    function (ele, input) {
                        localStorage.removeItem(`%s`);
                        localStorage.setItem(`%s`, %s);
                    }
                    ''' % (key, key, value))
                    print(f"设置localStorage: {key}")
                except Exception as e:
                    print(f"设置localStorage {key} 时出错: {e}")

        # 刷新页面应用设置
        print("刷新页面应用Cookie和localStorage...")
        web_page.navigate("https://www.xiaohongshu.com", load_timeout=20)
        sleep(3)

        # 验证登录状态
        if validate_login:
            return validate_login_status(web_page)
        else:
            print("Cookie设置完成（跳过登录验证）")
            return True

    except Exception as e:
        print(f"登录过程中出错: {e}")
        return False


def validate_login_status(web_page):
    """
    验证小红书登录状态

    Args:
        web_page: xbot网页对象

    Returns:
        bool: 是否已登录
    """
    print("验证登录状态...")

    try:
        sleep(2)

        # 检查页面源码
        page_source = web_page.execute_javascript('''
            function (ele, input) {
                return document.documentElement.outerHTML;
            }
        ''')

        # 检查是否包含登录相关关键词
        if "请登录" in page_source or '"登录"' in page_source:
            print("登录失败: 页面显示需要登录")
            return False

        # 尝试访问个人主页验证
        print("尝试访问个人主页验证...")
        current_url = web_page.get_url()
        web_page.navigate("https://www.xiaohongshu.com/user/profile", load_timeout=20)
        sleep(3)

        # 检查是否被重定向到登录页面
        new_url = web_page.get_url()
        if "login" in new_url.lower() or "signin" in new_url.lower():
            print("登录失败: 访问个人主页时被重定向到登录页面")
            return False

        # 检查个人主页内容
        profile_page_source = web_page.execute_javascript('''
            function (ele, input) {
                return document.documentElement.outerHTML;
            }
        ''')

        if "请登录" in profile_page_source:
            print("登录失败: 个人主页提示需要登录")
            return False

        print("登录成功: 成功访问个人主页")
        return True

    except Exception as e:
        print(f"验证登录状态时出错: {e}")
        return False


def clear_cookies(web_page):
    """
    清除网页Cookie

    Args:
        web_page: xbot网页对象
    """
    print("开始清除Cookie...")

    try:
        cookies = get_cookies(url_type="auto", browser=web_page, web_type=None, url=None,
                            name=None, domain=None, path=None, filter_secure=False,
                            secure=False, filter_session=False, session=False)

        for cookie in cookies:
            if cookie['name'] != "":
                try:
                    remove_cookie(url_type="auto", browser=web_page, web_type="cef",
                                url=None, remove_type="appoint_cookie", name=cookie['name'])
                except Exception as e:
                    print(f"删除Cookie {cookie['name']} 时出错: {e}")

        print("Cookie清除完成")

    except Exception as e:
        print(f"清除Cookie时出错: {e}")


def main(args):
    """
    主函数 - 小红书Cookie登录模块

    参数说明:
    - 网页对象: xbot网页浏览器对象
    - Cookie文件路径: Cookie文件的完整路径
    - 添加本地存储: 是否设置localStorage，默认True
    - 验证登录状态: 是否验证登录状态，默认True

    返回值:
    - True: 登录成功
    - False: 登录失败
    """
    # 参数解析
    if args is None:
        网页对象 = None
        Cookie文件路径 = ""
        添加本地存储 = True
        验证登录状态 = True
    else:
        网页对象 = args.get("网页对象", None)
        Cookie文件路径 = args.get("Cookie文件路径", "")
        添加本地存储 = args.get("添加本地存储", True)
        验证登录状态 = args.get("验证登录状态", True)

    try:
        print("=== 小红书Cookie登录模块启动 ===")
        print(f"参数信息:")
        print(f"  - Cookie文件路径: {Cookie文件路径}")
        print(f"  - 添加本地存储: {添加本地存储}")
        print(f"  - 验证登录状态: {验证登录状态}")

        # 参数验证
        if 网页对象 is None:
            error_msg = "错误: 网页对象不能为空，请先获取网页对象"
            print(error_msg)
            return False

        if not Cookie文件路径:
            error_msg = "错误: Cookie文件路径不能为空，请提供有效的Cookie文件路径"
            print(error_msg)
            return False

        # 执行小红书Cookie登录
        print("开始执行小红书Cookie登录...")
        登录结果 = xhs_cookie_login(
            web_page=网页对象,
            cookie_file_path=Cookie文件路径,
            add_local_storage=添加本地存储,
            validate_login=验证登录状态
        )

        if 登录结果:
            success_msg = "✅ 小红书Cookie登录成功！"
            print(success_msg)
            print("=== 登录流程完成 ===")
            return True
        else:
            error_msg = "❌ 小红书Cookie登录失败，请检查Cookie文件是否有效"
            print(error_msg)
            print("=== 登录流程结束 ===")
            return False

    except FileNotFoundError as e:
        error_msg = f"文件错误: Cookie文件未找到 - {e}"
        print(error_msg)
        return False
    except json.JSONDecodeError as e:
        error_msg = f"JSON解析错误: Cookie文件格式不正确 - {e}"
        print(error_msg)
        return False
    except Exception as e:
        error_msg = f"执行过程中出现未知异常: {type(e).__name__} - {e}"
        print(error_msg)
        print("=== 登录流程异常结束 ===")
        return False
    finally:
        # 清理工作（如果需要）
        pass
