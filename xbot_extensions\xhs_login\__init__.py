from .import package
import xbot_visual

def process1(网页对象, <PERSON><PERSON>文件路径, 验证登录状态=True):
    """
    小红书Cookie登录
    使用保存的Cookie实现小红书登录操作
    * @param 网页对象，网页对象
    * @param Cookie文件路径，<PERSON><PERSON>文件的完整路径
    * @param 验证登录状态，是否验证登录状态，默认为True
    """
    outputs = []
    inputs = {"网页对象": 网页对象, "Cookie文件路径": <PERSON><PERSON>文件路径, "验证登录状态": 验证登录状态}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.xhs_login.process1", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.xhs_login.process1", package=__name__, inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.xhs_login.process1", extension_module, activity_func)
