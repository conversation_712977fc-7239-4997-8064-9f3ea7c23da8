{"uuid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "小红书Cookie登录", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/images/cd/14/cd14a1498e34702aef3460b5eab1ee8a.png", "version": "1.0.0", "tags": "custom", "software": null, "software_title": null, "package_version": 2, "feature_list": [], "description": "该指令集实现了小红书网站的Cookie登录操作，支持Cookie设置、本地存储设置和登录状态验证", "instruction": null, "use_latest_pip": false, "videoName": null, "startup": "main", "robot_type": "activity", "activity_code": "xhs_login", "flows": [{"name": "main", "filename": "main", "kind": "Visual", "opened": false, "groupName": null}, {"name": "_core", "filename": "_core", "kind": "Code", "opened": false, "groupName": "py"}, {"name": "小红书Cookie登录", "filename": "process1", "kind": "Visual", "opened": true, "groupName": "小红书登录操作"}, {"name": "test", "filename": "test", "kind": "Code", "opened": false, "groupName": "py"}], "flow_groups": [{"name": "小红书登录操作"}, {"name": "py"}], "variables": [], "external_dependencies": [], "internaldependencies": [], "selectordependencies": [], "internalautodependencies": [], "ipaasDependencies": [], "databook_columns": [], "authority": "use", "internalautoupgrade": false, "isbrief": false, "uia_type": "PC", "persist_databook": false, "customItems": {}}