# xhs_rpa_cookie_login.py

import json
import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

def login_xhs_with_cookie(cookie_file_path):
    """
    使用保存的cookie在真实浏览器中登录小红书
    
    Args:
        cookie_file_path: cookie文件的路径
    """
    print(f"开始从 {cookie_file_path} 读取cookie...")
    
    # 检查cookie文件是否存在
    if not os.path.exists(cookie_file_path):
        print(f"错误: Cookie文件不存在: {cookie_file_path}")
        return False
    
    # 读取cookie文件
    try:
        with open(cookie_file_path, 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        
        # 检查cookie文件格式
        if not isinstance(cookie_data, dict):
            print(f"错误: Cookie文件格式不正确，应为JSON对象")
            return False
        
        cookies = cookie_data.get('cookies', [])
        local_storage = cookie_data.get('localStorage', {})
        
        print(f"成功读取 {len(cookies)} 个cookie和 {len(local_storage)} 个localStorage项")
    except Exception as e:
        print(f"读取cookie文件时出错: {e}")
        return False
    
    # 设置Chrome选项
    chrome_options = Options()
    # 如果需要无头模式（不显示浏览器界面），取消下面这行的注释
    # chrome_options.add_argument('--headless')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')  # 防止被检测为自动化工具
    
    # 启动Chrome浏览器
    print("启动Chrome浏览器...")
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # 首先访问小红书域名，以便能够设置cookie
        print("访问小红书网站...")
        driver.get("https://www.xiaohongshu.com")
        time.sleep(2)  # 等待页面加载
        
        # 设置cookie
        print("开始设置cookie...")
        for cookie in cookies:
            # 确保cookie有必要的字段
            if 'name' in cookie and 'value' in cookie:
                # 删除可能导致问题的字段
                cookie_dict = {
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie.get('domain', '.xiaohongshu.com'),
                    'path': cookie.get('path', '/'),
                }
                
                # 可选字段
                if 'expiry' in cookie:
                    cookie_dict['expiry'] = cookie['expiry']
                if 'secure' in cookie:
                    cookie_dict['secure'] = cookie['secure']
                if 'httpOnly' in cookie:
                    cookie_dict['httpOnly'] = cookie['httpOnly']
                
                try:
                    driver.add_cookie(cookie_dict)
                    print(f"设置cookie: {cookie['name']}")
                except Exception as e:
                    print(f"设置cookie {cookie['name']} 时出错: {e}")
        
        # 设置localStorage
        print("开始设置localStorage...")
        if local_storage:
            # 执行JavaScript来设置localStorage
            for key, value in local_storage.items():
                script = f"localStorage.setItem('{key}', '{value}');"
                try:
                    driver.execute_script(script)
                    print(f"设置localStorage: {key}")
                except Exception as e:
                    print(f"设置localStorage {key} 时出错: {e}")
        
        # 刷新页面以应用cookie和localStorage
        print("刷新页面以应用cookie和localStorage...")
        driver.refresh()
        time.sleep(3)  # 等待页面加载
        
        # 验证登录状态
        print("验证登录状态...")
        try:
            # 等待页面加载完成
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 检查是否有登录按钮（未登录状态）
            login_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '登录')]")
            if login_buttons:
                print("登录失败: 页面上仍然显示登录按钮")
                return False
            
            # 检查是否有用户头像（已登录状态）
            avatar_elements = driver.find_elements(By.XPATH, "//div[contains(@class, 'avatar')]")
            if avatar_elements:
                print("登录成功: 检测到用户头像")
                return True
            
            # 如果以上检查都不确定，尝试访问个人主页
            print("尝试访问需要登录的页面来验证...")
            driver.get("https://www.xiaohongshu.com/user/profile")
            time.sleep(3)
            
            # 再次检查登录状态
            if "请登录" in driver.page_source:
                print("登录失败: 访问个人主页时提示需要登录")
                return False
            else:
                print("登录成功: 成功访问个人主页")
                return True
                
        except TimeoutException:
            print("等待页面加载超时")
            return False
        except Exception as e:
            print(f"验证登录状态时出错: {e}")
            return False
    
    finally:
        # 等待用户查看结果
        input("按Enter键关闭浏览器...")
        driver.quit()

# 主函数
def main():
    cookie_file_path = r"C:\Users\<USER>\Downloads\cookie.json"
    login_result = login_xhs_with_cookie(cookie_file_path)
    if login_result:
        print("小红书登录成功！")
    else:
        print("小红书登录失败，请检查cookie是否有效。")

if __name__ == "__main__":
    main()