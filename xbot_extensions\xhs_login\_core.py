# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import xbot
import xbot_visual
from xbot_visual.web import get_cookies, set_cookie, remove_cookie
from xbot import print, sleep
from .import package
from .package import variables as glv
import json
import os
import time

print = [lambda *_, **__: None, print][__package__ == "xbot_robot" or hasattr(xbot, "__log")]


def xhs_cookie_login(web_page, cookie_file_path, *, add_local_storage=True):
    """
    使用保存的Cookie在小红书网站实现登录
    
    Args:
        web_page: xbot网页对象
        cookie_file_path: cookie文件的路径
        add_local_storage: 是否添加本地存储，默认为True
    
    Returns:
        bool: 登录是否成功
    """
    print(f"开始从 {cookie_file_path} 读取cookie...")
    
    # 检查cookie文件是否存在
    if not os.path.exists(cookie_file_path):
        print(f"错误: Cookie文件不存在: {cookie_file_path}")
        return False
    
    # 读取cookie文件
    try:
        with open(cookie_file_path, 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        
        # 检查cookie文件格式
        if not isinstance(cookie_data, dict):
            print(f"错误: Cookie文件格式不正确，应为JSON对象")
            return False
        
        cookies = cookie_data.get('cookies', [])
        local_storage = cookie_data.get('localStorage', {})
        
        print(f"成功读取 {len(cookies)} 个cookie和 {len(local_storage)} 个localStorage项")
    except Exception as e:
        print(f"读取cookie文件时出错: {e}")
        return False
    
    try:
        # 首先访问小红书域名，以便能够设置cookie
        print("访问小红书网站...")
        web_page.navigate("https://www.xiaohongshu.com", load_timeout=20)
        sleep(2)  # 等待页面加载
        
        # 清除现有cookie（退出登录）
        print("清除现有cookie...")
        logout_xhs(web_page)
        
        # 设置cookie
        print("开始设置cookie...")
        for cookie in cookies:
            # 确保cookie有必要的字段
            if 'name' in cookie and 'value' in cookie and cookie['name'] != "":
                # 构建cookie字典，适配xbot的set_cookie函数
                cookie_dict = {
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie.get('domain', '.xiaohongshu.com'),
                    'path': cookie.get('path', '/'),
                }
                
                # 可选字段
                if 'expires' in cookie and cookie['expires']:
                    cookie_dict['expires'] = cookie['expires']
                if 'secure' in cookie:
                    cookie_dict['secure'] = cookie['secure']
                if 'httpOnly' in cookie:
                    cookie_dict['httpOnly'] = cookie['httpOnly']
                
                try:
                    set_cookie(url_type="auto", browser=web_page, web_type=None, url=None, **cookie_dict)
                    print(f"设置cookie: {cookie['name']}")
                except Exception as e:
                    print(f"设置cookie {cookie['name']} 时出错: {e}")
        
        # 设置localStorage
        if add_local_storage and local_storage:
            print("开始设置localStorage...")
            for key, value in local_storage.items():
                try:
                    if isinstance(value, str):
                        value = f'`{value}`'
                    web_page.execute_javascript('''
                    function (ele, input) {
                        localStorage.removeItem(`%s`);
                        localStorage.setItem(`%s`, %s);
                    }
                    ''' % (key, key, value))
                    print(f"设置localStorage: {key}")
                except Exception as e:
                    print(f"设置localStorage {key} 时出错: {e}")
        
        # 刷新页面以应用cookie和localStorage
        print("刷新页面以应用cookie和localStorage...")
        web_page.navigate("https://www.xiaohongshu.com", load_timeout=20)
        sleep(3)  # 等待页面加载
        
        return True
        
    except Exception as e:
        print(f"登录过程中出错: {e}")
        return False


def validate_login_status(web_page):
    """
    验证小红书登录状态
    
    Args:
        web_page: xbot网页对象
    
    Returns:
        bool: 是否已登录
    """
    print("验证登录状态...")
    
    try:
        # 等待页面加载完成
        sleep(2)
        
        # 检查页面源码中是否包含登录相关的关键词
        page_source = web_page.execute_javascript('''
            function (ele, input) {
                return document.documentElement.outerHTML;
            }
        ''')
        
        # 如果页面包含"请登录"或"登录"按钮，说明未登录
        if "请登录" in page_source or '"登录"' in page_source:
            print("登录失败: 页面显示需要登录")
            return False
        
        # 尝试访问需要登录的页面来验证
        print("尝试访问需要登录的页面来验证...")
        current_url = web_page.get_url()
        web_page.navigate("https://www.xiaohongshu.com/user/profile", load_timeout=20)
        sleep(3)
        
        # 检查是否被重定向到登录页面
        new_url = web_page.get_url()
        if "login" in new_url.lower() or "signin" in new_url.lower():
            print("登录失败: 访问个人主页时被重定向到登录页面")
            return False
        
        # 检查页面内容
        profile_page_source = web_page.execute_javascript('''
            function (ele, input) {
                return document.documentElement.outerHTML;
            }
        ''')
        
        if "请登录" in profile_page_source:
            print("登录失败: 个人主页提示需要登录")
            return False
        
        print("登录成功: 成功访问个人主页")
        return True
        
    except Exception as e:
        print(f"验证登录状态时出错: {e}")
        return False


def logout_xhs(web_page):
    """
    小红书退出登录（清除Cookie）
    
    Args:
        web_page: xbot网页对象
    """
    print("开始清除小红书Cookie...")
    
    try:
        cookies = get_cookies(url_type="auto", browser=web_page, web_type=None, url=None, 
                            name=None, domain=None, path=None, filter_secure=False, 
                            secure=False, filter_session=False, session=False)
        
        for cookie in cookies:
            if cookie['name'] != "":
                try:
                    remove_cookie(url_type="auto", browser=web_page, web_type="cef", 
                                url=None, remove_type="appoint_cookie", name=cookie['name'])
                except Exception as e:
                    print(f"删除cookie {cookie['name']} 时出错: {e}")
        
        print("Cookie清除完成")
        
    except Exception as e:
        print(f"清除Cookie时出错: {e}")


def main(args):
    pass
